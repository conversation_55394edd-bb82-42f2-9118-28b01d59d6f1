// components/Header.tsx
import Link from "next/link";

export default function Header() {
  return (
    <header className="bg-dark px-4 py-3 border-b border-gray-700 text-white">
      <div className="max-w-6xl mx-auto flex justify-between items-center">
        <Link href="/" className="text-2xl font-bold text-accent">
          JackLibra
        </Link>
        <nav className="space-x-6 text-lg">
          <Link href="/projects" className="hover:text-accent">Projects</Link>
          <Link href="/blog" className="hover:text-accent">Blog</Link>
          <Link href="https://github.com/Jack-Libra" target="_blank" className="hover:text-accent">GitHub</Link>
        </nav>
      </div>
    </header>
  );
}
