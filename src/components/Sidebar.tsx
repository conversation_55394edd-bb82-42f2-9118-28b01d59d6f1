import Link from 'next/link';

export default function Sidebar() {
  return (
    <aside className="w-64 bg-card border-r border-custom p-6 hidden lg:block min-h-screen">
      <div className="space-y-8">
        {/* 快速導航 */}
        <div>
          <h3 className="text-lg font-semibold mb-4 text-accent">快速導航</h3>
          <ul className="space-y-3">
            <li>
              <Link
                href="/"
                className="flex items-center space-x-2 text-gray-300 hover:text-accent transition-colors p-2 rounded-lg hover:bg-gray-800/50"
              >
                <span>🏠</span>
                <span>首頁</span>
              </Link>
            </li>
            <li>
              <Link
                href="/projects"
                className="flex items-center space-x-2 text-gray-300 hover:text-accent transition-colors p-2 rounded-lg hover:bg-gray-800/50"
              >
                <span>📁</span>
                <span>專案列表</span>
              </Link>
            </li>
          </ul>
        </div>

        {/* 專案列表 */}
        <div>
          <h3 className="text-lg font-semibold mb-4 text-accent">專案</h3>
          <ul className="space-y-3">
            <li>
              <Link
                href="/projects/ecommerce-trend"
                className="flex items-center space-x-2 text-gray-300 hover:text-accent transition-colors p-2 rounded-lg hover:bg-gray-800/50"
              >
                <span>📊</span>
                <span>電商趨勢分析</span>
              </Link>
            </li>
            {/* 未來可以添加更多專案 */}
          </ul>
        </div>

        {/* 外部連結 */}
        <div>
          <h3 className="text-lg font-semibold mb-4 text-accent">外部連結</h3>
          <ul className="space-y-3">
            <li>
              <a
                href="https://medium.com/@dafsf60804"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 text-gray-300 hover:text-accent transition-colors p-2 rounded-lg hover:bg-gray-800/50"
              >
                <span>📝</span>
                <span>Medium 文章</span>
              </a>
            </li>
            <li>
              <a
                href="https://github.com/Jack-Libra"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 text-gray-300 hover:text-accent transition-colors p-2 rounded-lg hover:bg-gray-800/50"
              >
                <span>💻</span>
                <span>GitHub</span>
              </a>
            </li>
          </ul>
        </div>

        {/* 技能標籤 */}
        <div>
          <h3 className="text-lg font-semibold mb-4 text-accent">技能</h3>
          <div className="flex flex-wrap gap-2">
            <span className="px-3 py-1 bg-primary/20 text-primary text-xs rounded-full">Python</span>
            <span className="px-3 py-1 bg-secondary/20 text-secondary text-xs rounded-full">Next.js</span>
            <span className="px-3 py-1 bg-accent/20 text-accent text-xs rounded-full">FastAPI</span>
            <span className="px-3 py-1 bg-green-500/20 text-green-400 text-xs rounded-full">Supabase</span>
            <span className="px-3 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full">Tailwind</span>
          </div>
        </div>
      </div>
    </aside>
  );
}
