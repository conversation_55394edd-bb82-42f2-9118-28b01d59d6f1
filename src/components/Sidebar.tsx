// components/Sidebar.tsx
export default function Sidebar() {
  return (
    <aside className="w-64 bg-dark border-r border-gray-700 p-4 hidden md:block">
      <h3 className="text-xl font-bold mb-4 text-accent">Projects</h3>
      <ul className="space-y-2 text-gray-300">
        <li><a href="/projects/ecommerce-trend" className="hover:text-accent">Ecommerce Trend</a></li>
        {/* Add more in future */}
      </ul>
    </aside>
  );
}
