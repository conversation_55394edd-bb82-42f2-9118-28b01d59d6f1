// app/projects/page.tsx
import Image from "next/image";
import Link from "next/link";

export default function ProjectsPage() {
  return (
    <main className="max-w-4xl mx-auto px-4 py-8">
      <h1 className="text-4xl font-bold mb-6 text-accent">Projects 專案展示</h1>
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-2">📈 Ecommerce Trend Analyzer</h2>
        <p className="mb-2 text-gray-200">
          A platform that analyzes the popularity of ecommerce products through web crawling and scheduled snapshots. Built with Supabase, Python FastAPI, and visualized via Next.js.
        </p>
        <p className="mb-4 text-gray-200">
          本專案透過定時爬蟲與趨勢快照，分析電商平台上商品熱度變化，並以圖表方式視覺化呈現。使用 Supabase 作為資料倉儲，FastAPI 建立 API，並透過 Next.js 前端動態顯示。
        </p>
        <Link
          href="https://ecommerce-trend-analyzer.vercel.app"
          target="_blank"
          className="inline-flex items-center gap-2 px-4 py-2 bg-accent text-black font-semibold rounded hover:opacity-90 transition"
        >
          🔗 前往專案 Vercel 網頁
        </Link>
        <div className="mt-6">
          <Image
            src="/ecommerce-preview.png"
            alt="Ecommerce Trend Platform Preview"
            width={1000}
            height={600}
            className="rounded-xl border border-gray-600"
          />
        </div>
      </section>
    </main>
  );
}
