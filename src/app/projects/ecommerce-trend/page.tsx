// src/app/projects/ecommerce-trend/page.tsx
import Image from 'next/image'
import Link from 'next/link'

export default function EcommerceTrendPage() {
  return (
    <main className="max-w-3xl mx-auto p-8">
      <h1 className="text-4xl font-bold mb-4">📈 電商趨勢分析平台</h1>

      <p className="mb-6 text-lg">
        本平台整合 Supabase、n8n、自動化爬蟲與 GitHub Actions，
        每日自動抓取與更新電商熱門趨勢，並透過 Vercel 快速部署展示。
      </p>

      <a href="https://ecommerce-trend-analyzer.vercel.app" target="_blank" rel="noopener noreferrer">
        <Image
          src="/images/ecommerce-preview.png"
          alt="電商趨勢分析平台"
          width={900}
          height={500}
          className="rounded-xl shadow-xl hover:opacity-90 transition"
        />
      </a>

      <div className="mt-6">
        <Link
          href="https://ecommerce-trend-analyzer.vercel.app"
          target="_blank"
          className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-3 rounded-xl shadow"
        >
          🚀 前往專案網站
        </Link>
      </div>

      <section className="mt-10">
        <h2 className="text-2xl font-semibold mb-2">使用技術</h2>
        <ul className="list-disc list-inside text-gray-800 leading-loose">
          <li>Next.js (App Router + SSG)</li>
          <li>Tailwind CSS</li>
          <li>Supabase（儲存每日快照資料）</li>
          <li>n8n 自動爬蟲與資料流</li>
          <li>GitHub Actions 自動部署到 Vercel</li>
        </ul>
      </section>
    </main>
  )
}
