import Image from 'next/image';
import Link from 'next/link';

export default function EcommerceTrendPage() {
  return (
    <div className="min-h-screen p-6 lg:p-12">
      <div className="max-w-5xl mx-auto">
        {/* Breadcrumb */}
        <nav className="mb-8 text-sm">
          <Link href="/" className="text-secondary hover:text-accent transition-colors">
            首頁
          </Link>
          <span className="mx-2 text-secondary">/</span>
          <Link href="/projects" className="text-secondary hover:text-accent transition-colors">
            專案
          </Link>
          <span className="mx-2 text-secondary">/</span>
          <span className="text-accent">電商趨勢分析平台</span>
        </nav>

        {/* Header */}
        <div className="text-center mb-12 animate-fade-in-up">
          <div className="flex items-center justify-center gap-4 mb-6">
            <span className="text-5xl">📊</span>
            <h1 className="text-4xl lg:text-6xl font-bold gradient-text">
              電商趨勢分析平台
            </h1>
          </div>
          <p className="text-xl text-secondary max-w-4xl mx-auto leading-relaxed">
            整合 Supabase、n8n、自動化爬蟲與 GitHub Actions，每日自動抓取與更新電商熱門趨勢，
            並透過 Vercel 快速部署展示的全自動化數據分析平台。
          </p>
        </div>

        {/* Main Image */}
        <section className="mb-16">
          <div className="relative">
            <a
              href="https://ecommerce-trend-analyzer.vercel.app"
              target="_blank"
              rel="noopener noreferrer"
              className="block"
            >
              <Image
                src="/images/ecommerce-preview.png"
                alt="電商趨勢分析平台預覽"
                width={1200}
                height={700}
                className="rounded-2xl shadow-2xl hover:scale-105 transition-transform duration-300 cursor-pointer w-full"
              />
            </a>
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl pointer-events-none"></div>
          </div>
        </section>

        {/* Action Buttons */}
        <section className="text-center mb-16">
          <div className="flex flex-wrap gap-4 justify-center">
            <a
              href="https://ecommerce-trend-analyzer.vercel.app"
              target="_blank"
              rel="noopener noreferrer"
              className="btn-primary text-lg px-8 py-4"
            >
              🚀 查看專案網站
            </a>
            <a
              href="https://github.com/Jack-Libra"
              target="_blank"
              rel="noopener noreferrer"
              className="btn-secondary text-lg px-8 py-4"
            >
              💻 GitHub 原始碼
            </a>
          </div>
        </section>

        {/* Project Overview */}
        <section className="mb-16">
          <div className="grid lg:grid-cols-2 gap-12">
            <div className="card">
              <h2 className="text-2xl font-bold mb-6 text-accent">專案特色</h2>
              <ul className="space-y-4">
                <li className="flex items-start gap-3">
                  <span className="text-primary text-xl">🤖</span>
                  <div>
                    <h3 className="font-semibold text-white">全自動化爬蟲</h3>
                    <p className="text-secondary">使用 n8n 建立自動化工作流程，定時抓取電商平台數據</p>
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <span className="text-secondary text-xl">📊</span>
                  <div>
                    <h3 className="font-semibold text-white">趨勢視覺化</h3>
                    <p className="text-secondary">透過圖表清楚呈現商品熱度變化和市場趨勢</p>
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <span className="text-accent text-xl">⚡</span>
                  <div>
                    <h3 className="font-semibold text-white">即時更新</h3>
                    <p className="text-secondary">GitHub Actions 自動部署，確保數據即時性</p>
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <span className="text-green-400 text-xl">🗄️</span>
                  <div>
                    <h3 className="font-semibold text-white">雲端儲存</h3>
                    <p className="text-secondary">使用 Supabase 作為資料倉儲，確保數據安全性</p>
                  </div>
                </li>
              </ul>
            </div>

            <div className="card">
              <h2 className="text-2xl font-bold mb-6 text-accent">技術架構</h2>
              <div className="space-y-4">
                <div className="flex items-center gap-3 p-3 bg-primary/10 rounded-lg">
                  <span className="text-2xl">🐍</span>
                  <div>
                    <h3 className="font-semibold text-primary">Python & FastAPI</h3>
                    <p className="text-sm text-secondary">後端 API 開發與數據處理</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-secondary/10 rounded-lg">
                  <span className="text-2xl">⚛️</span>
                  <div>
                    <h3 className="font-semibold text-secondary">Next.js</h3>
                    <p className="text-sm text-secondary">現代化前端框架與 SSG</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-accent/10 rounded-lg">
                  <span className="text-2xl">🎨</span>
                  <div>
                    <h3 className="font-semibold text-accent">Tailwind CSS</h3>
                    <p className="text-sm text-secondary">響應式 UI 設計</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-green-500/10 rounded-lg">
                  <span className="text-2xl">🗄️</span>
                  <div>
                    <h3 className="font-semibold text-green-400">Supabase</h3>
                    <p className="text-sm text-secondary">PostgreSQL 資料庫服務</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-orange-500/10 rounded-lg">
                  <span className="text-2xl">🔄</span>
                  <div>
                    <h3 className="font-semibold text-orange-400">n8n</h3>
                    <p className="text-sm text-secondary">自動化工作流程</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-purple-500/10 rounded-lg">
                  <span className="text-2xl">🚀</span>
                  <div>
                    <h3 className="font-semibold text-purple-400">GitHub Actions</h3>
                    <p className="text-sm text-secondary">CI/CD 自動部署</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Development Process */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold mb-8 text-center">開發流程</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="card text-center">
              <div className="text-4xl mb-4">🔍</div>
              <h3 className="text-xl font-semibold mb-3 text-accent">數據收集</h3>
              <p className="text-secondary">
                使用 n8n 建立自動化爬蟲，定時從電商平台收集商品數據和熱度指標。
              </p>
            </div>
            <div className="card text-center">
              <div className="text-4xl mb-4">⚙️</div>
              <h3 className="text-xl font-semibold mb-3 text-accent">數據處理</h3>
              <p className="text-secondary">
                透過 Python 和 FastAPI 處理原始數據，計算趨勢指標並儲存至 Supabase。
              </p>
            </div>
            <div className="card text-center">
              <div className="text-4xl mb-4">📊</div>
              <h3 className="text-xl font-semibold mb-3 text-accent">視覺化展示</h3>
              <p className="text-secondary">
                使用 Next.js 建立響應式前端，以圖表形式呈現趨勢分析結果。
              </p>
            </div>
          </div>
        </section>

        {/* Back to Projects */}
        <section className="text-center">
          <Link href="/projects" className="btn-accent text-lg px-8 py-4">
            ← 返回專案列表
          </Link>
        </section>
      </div>
    </div>
  );
}
